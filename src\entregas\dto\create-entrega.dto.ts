import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty } from "class-validator";

export class CreateEntregaDto {
  @ApiProperty({
    description: 'URL da entrega',
    example: 'https://example.com/entrega'
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'Nome do cliente',
    example: '<PERSON>'
  })
  @IsString()
  @IsNotEmpty()
  customer_name: string;
}
