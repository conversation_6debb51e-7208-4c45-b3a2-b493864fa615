// Generated by Xata Codegen 0.30.1. Please do not edit.
import { buildClient } from "@xata.io/client";
import type {
  BaseClientOptions,
  SchemaInference,
  XataRecord,
} from "@xata.io/client";

const tables = [
  {
    name: "Users",
    columns: [
      { name: "fullname", type: "string" },
      { name: "password", type: "string" },
      { name: "email", type: "email", unique: true },
      { name: "type", type: "string" },
      { name: "cellphone_number", type: "string" },
      { name: "cellphone_number_aux", type: "string" },
      { name: "gender", type: "string" },
      { name: "date_birth", type: "datetime" },
      { name: "cgc", type: "string", unique: true },
      {
        name: "avatar_url",
        type: "text",
        notNull: true,
        defaultValue: "user.jpg",
      },
      { name: "date_birth_BR", type: "string" },
      { name: "picture", type: "file", file: { defaultPublicAccess: true } },
      { name: "last_online", type: "datetime" },
      { name: "password_code", type: "string" },
      { name: "asaas_id", type: "string" },
    ],
    revLinks: [
      { column: "usuario", table: "Solicitacoes" },
      { column: "colaborador", table: "Solicitacoes" },
      { column: "colaborador_id", table: "HistoricoSolicitacoes" },
      { column: "user", table: "Chat" },
      { column: "collab", table: "Chat" },
      { column: "user_id", table: "Entregas" },
    ],
  },
  {
    name: "Solicitacoes",
    columns: [
      { name: "titulo", type: "string" },
      { name: "link_dados", type: "string" },
      { name: "etapa", type: "string" },
      { name: "situacao_etapa", type: "string" },
      { name: "usuario", type: "link", link: { table: "Users" } },
      { name: "objetivo_geral", type: "text" },
      { name: "objetivos_especificos", type: "string" },
      { name: "hipoteses", type: "text" },
      { name: "variaveis", type: "text" },
      { name: "detalhamentos", type: "text" },
      { name: "instrumentos", type: "text" },
      { name: "instrumentos_referencias", type: "text" },
      { name: "metodologia", type: "text" },
      { name: "colaborador", type: "link", link: { table: "Users" } },
      { name: "viabilidade_descricao", type: "text" },
      { name: "viabilidade_link", type: "string" },
      { name: "viabilidade_valor", type: "float" },
      { name: "viabilidade_prazo", type: "datetime" },
      { name: "tipo", type: "string" },
      { name: "observacoes", type: "text" },
      { name: "status", type: "string" },
      { name: "entrega_link", type: "string" },
      { name: "variaveis_dependentes", type: "text" },
    ],
    revLinks: [
      { column: "solicitacao_id", table: "HistoricoSolicitacoes" },
      { column: "solicitacao", table: "Payments" },
    ],
  },
  {
    name: "HistoricoSolicitacoes",
    columns: [
      { name: "solicitacao_id", type: "link", link: { table: "Solicitacoes" } },
      { name: "colaborador_id", type: "link", link: { table: "Users" } },
      { name: "descricao", type: "string" },
    ],
  },
  {
    name: "Payments",
    columns: [
      {
        name: "name",
        type: "string",
        notNull: true,
        defaultValue: "Pagamento",
      },
      { name: "description", type: "string" },
      { name: "endDate", type: "string" },
      { name: "value", type: "float" },
      {
        name: "billingType",
        type: "string",
        notNull: true,
        defaultValue: "UNDEFINED",
      },
      {
        name: "chargeType",
        type: "string",
        notNull: true,
        defaultValue: "DETACHED",
      },
      { name: "dueDateLimitDays", type: "int" },
      { name: "asaas_id", type: "string" },
      { name: "url", type: "string" },
      { name: "solicitacao", type: "link", link: { table: "Solicitacoes" } },
      { name: "status", type: "string" },
      { name: "paymentDate", type: "datetime" },
    ],
  },
  {
    name: "Acessos",
    columns: [
      { name: "latitude", type: "float" },
      { name: "longitude", type: "float" },
      { name: "quantidade", type: "int" },
    ],
  },
  {
    name: "Chat",
    columns: [
      { name: "datetime", type: "datetime" },
      { name: "user", type: "link", link: { table: "Users" } },
      { name: "collab", type: "link", link: { table: "Users" } },
      { name: "message", type: "text" },
      { name: "room_id", type: "string" },
      { name: "seen", type: "bool", defaultValue: "false" },
    ],
  },
  {
    name: "Livros",
    columns: [
      { name: "titulo", type: "text", notNull: true, defaultValue: "-" },
      { name: "idioma", type: "text", notNull: true, defaultValue: "ptBR" },
      { name: "dt_publicacao", type: "datetime" },
      { name: "nro_paginas", type: "int" },
      { name: "autor", type: "text" },
      { name: "editora", type: "text" },
      { name: "resumo", type: "text" },
      { name: "link", type: "text" },
      { name: "url_imagem", type: "text" },
      { name: "acessos", type: "int", notNull: true, defaultValue: "0" },
    ],
  },
  {
    name: "Entregas",
    columns: [
      { name: "user_id", type: "link", link: { table: "Users" } },
      { name: "url", type: "text" },
      { name: "customer_name", type: "text" },
      { name: "code", type: "text" },
      { name: "unlocked", type: "bool", defaultValue: "false" },
    ],
  },
] as const;

export type SchemaTables = typeof tables;
export type InferredTypes = SchemaInference<SchemaTables>;

export type Users = InferredTypes["Users"];
export type UsersRecord = Users & XataRecord;

export type Solicitacoes = InferredTypes["Solicitacoes"];
export type SolicitacoesRecord = Solicitacoes & XataRecord;

export type HistoricoSolicitacoes = InferredTypes["HistoricoSolicitacoes"];
export type HistoricoSolicitacoesRecord = HistoricoSolicitacoes & XataRecord;

export type Payments = InferredTypes["Payments"];
export type PaymentsRecord = Payments & XataRecord;

export type Acessos = InferredTypes["Acessos"];
export type AcessosRecord = Acessos & XataRecord;

export type Chat = InferredTypes["Chat"];
export type ChatRecord = Chat & XataRecord;

export type Livros = InferredTypes["Livros"];
export type LivrosRecord = Livros & XataRecord;

export type Entregas = InferredTypes["Entregas"];
export type EntregasRecord = Entregas & XataRecord;

export type DatabaseSchema = {
  Users: UsersRecord;
  Solicitacoes: SolicitacoesRecord;
  HistoricoSolicitacoes: HistoricoSolicitacoesRecord;
  Payments: PaymentsRecord;
  Acessos: AcessosRecord;
  Chat: ChatRecord;
  Livros: LivrosRecord;
  Entregas: EntregasRecord;
};

const DatabaseClient = buildClient();

const defaultOptions = {
  databaseURL: "https://Habilis-r643vm.us-east-1.xata.sh/db/habilis",
};

export class XataClient extends DatabaseClient<DatabaseSchema> {
  constructor(options?: BaseClientOptions) {
    super({ ...defaultOptions, ...options }, tables);
  }
}

let instance: XataClient | undefined = undefined;

export const getXataClient = () => {
  if (instance) return instance;

  instance = new XataClient();
  return instance;
};
