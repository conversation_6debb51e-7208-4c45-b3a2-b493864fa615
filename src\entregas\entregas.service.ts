import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { xata } from 'src/helpers/xata';
import { CreateEntregaDto } from './dto/create-entrega.dto';
import { validateBearer } from 'src/helpers/utils';

@Injectable()
export class EntregasService {
  async create(bearer: string, createEntregaDto: CreateEntregaDto) {
    const validate = await validateBearer(bearer);
    if (!validate.success) throw new UnauthorizedException();
    if (!validate.usuario) throw new UnauthorizedException();
    if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException('Apenas colaboradores podem criar entregas');

    const { url, customer_name } = createEntregaDto;

    // Validações
    if (!url.trim()) throw new BadRequestException('URL é obrigatória');
    if (!customer_name.trim()) throw new BadRequestException('Nome do cliente é obrigatório');

    // Gerar código alfanumérico único de 5 caracteres
    const code = await this.generateCode(5);

    const recordNew = await xata.db.Entregas.create({
      user_id: validate.usuario.id,
      url: url.trim(),
      customer_name: customer_name.trim(),
      code
    });

    if (!recordNew) {
      throw new BadRequestException('Erro ao criar entrega');
    }

    return recordNew;
  }

  async findAll(bearer: string) {
    const validate = await validateBearer(bearer);
    if (!validate.success) throw new UnauthorizedException();
    if (!validate.usuario) throw new UnauthorizedException();
    if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException('Apenas colaboradores podem visualizar entregas');

    // Se for colaborador (C), mostrar apenas suas entregas
    // Se for admin (A), mostrar todas as entregas
    let filter = {};
    if (validate.usuario.type === 'C') {
      filter = { user_id: validate.usuario.id };
    }

    const entregas = await xata.db.Entregas
      .select(['*', 'user_id.fullname', 'user_id.email'])
      .filter(filter)
      .sort('xata.createdAt', 'desc')
      .getAll();

    return entregas;
  }

  async findOne(bearer: string, id: string) {
    const validate = await validateBearer(bearer);
    if (!validate.success) throw new UnauthorizedException();
    if (!validate.usuario) throw new UnauthorizedException();
    if (!['A', 'C'].includes(validate.usuario.type)) throw new UnauthorizedException('Apenas colaboradores podem visualizar entregas');

    const entrega = await xata.db.Entregas
      .select(['*', 'user_id.fullname', 'user_id.email'])
      .filter({ id })
      .getFirst();

    if (!entrega) {
      throw new BadRequestException('Entrega não encontrada');
    }

    // Se for colaborador (C), verificar se a entrega pertence a ele
    if (validate.usuario.type === 'C' && entrega.user_id?.id !== validate.usuario.id) {
      throw new UnauthorizedException('Você não tem permissão para visualizar esta entrega');
    }

    return entrega;
  }

  private async generateCode(length: number): Promise<string> {
    let attempts = 0;
    const maxAttempts = 10; // Limite de tentativas para evitar loop infinito

    while (attempts < maxAttempts) {
      let result = '';
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      const charactersLength = characters.length;
      let counter = 0;

      while (counter < length) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
      }

      // Verificar se o código já existe
      const existingCode = await xata.db.Entregas.filter({ code: result }).getFirst();
      if (!existingCode) {
        return result; // Código único encontrado
      }

      attempts++;
    }

    // Se não conseguir gerar um código único após várias tentativas
    throw new BadRequestException('Erro ao gerar código único. Tente novamente.');
  }
}
