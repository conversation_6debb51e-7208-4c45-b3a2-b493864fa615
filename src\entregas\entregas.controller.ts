import { Controller, Get, Post, Body, Param, Headers } from '@nestjs/common';
import { EntregasService } from './entregas.service';
import { CreateEntregaDto } from './dto/create-entrega.dto';
import { ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { Entrega } from './entities/entrega.entity';

@ApiTags('entregas')
@Controller('entregas')
export class EntregasController {
  constructor(private readonly entregasService: EntregasService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Criar nova entrega' })
  @ApiResponse({
    status: 201,
    description: 'Entrega criada com sucesso',
    type: Entrega,
  })
  @ApiResponse({
    status: 400,
    description: 'Dados inválidos',
  })
  @ApiResponse({
    status: 401,
    description: 'Não autorizado - apenas colaboradores podem criar entregas',
  })
  create(@Headers() Headers, @Body() createEntregaDto: CreateEntregaDto) {
    return this.entregasService.create(Headers.authorization, createEntregaDto);
  }

  @Get()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Buscar todas as entregas' })
  @ApiResponse({
    status: 200,
    description: 'Lista de entregas',
    type: [Entrega],
  })
  @ApiResponse({
    status: 401,
    description: 'Não autorizado - apenas colaboradores podem visualizar entregas',
  })
  findAll(@Headers() Headers) {
    return this.entregasService.findAll(Headers.authorization);
  }

  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Buscar uma entrega específica' })
  @ApiResponse({
    status: 200,
    description: 'Entrega encontrada',
    type: Entrega,
  })
  @ApiResponse({
    status: 400,
    description: 'Entrega não encontrada',
  })
  @ApiResponse({
    status: 401,
    description: 'Não autorizado',
  })
  findOne(@Headers() Headers, @Param('id') id: string) {
    return this.entregasService.findOne(Headers.authorization, id);
  }
}
