import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './user/user.module';
import { SolicitacaoModule } from './solicitacao/solicitacao.module';
import { PaymentsModule } from './payments/payments.module';
import { SocketModule } from './socket/socket.module';
import { ChatModule } from './chat/chat.module';
import { BooksModule } from './books/books.module';
import { AuthModule } from './auth/auth.module';
import { EntregasModule } from './entregas/entregas.module';

@Module({
  imports: [UserModule, SolicitacaoModule, PaymentsModule, SocketModule, ChatModule, BooksModule, AuthModule, EntregasModule],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
